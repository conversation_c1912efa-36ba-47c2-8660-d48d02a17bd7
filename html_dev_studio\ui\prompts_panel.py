import os
import json
from datetime import datetime
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
                             QListWidget, QListWidgetItem, QTextEdit, QLineEdit,
                             QPushButton, QToolBar, QMenu, QMessageBox,
                             QFileDialog, QLabel, QInputDialog, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QIcon, QAction

class PromptsPanel(QWidget):
    """
    A widget for managing AI prompts, including history, templates, and associations.
    """
    # Signals
    prompt_selected = pyqtSignal(str) # prompt_text
    prompt_to_apply = pyqtSignal(str, str) # prompt_text, associated_file_path (optional)
    prompt_associated = pyqtSignal(str, str) # prompt_id, file_path

    def __init__(self, file_manager, parent=None):
        super().__init__(parent)
        self.file_manager = file_manager
        self.prompts_data_file = "ai_prompts_history.json" # Stored in project root or app data
        self.prompts_history = [] # List of dicts: {'id': str, 'text': str, 'timestamp': str, 'associated_file': str, 'tags': list}
        self.prompt_templates = [
            {"name": "Create responsive navigation bar", "text": "Create a responsive navigation bar using HTML and CSS. It should have a logo on the left, navigation links in the center that collapse into a hamburger menu on mobile, and a call-to-action button on the right."},
            {"name": "Generate CSS grid layout", "text": "Generate a CSS grid layout for a webpage. The layout should have a header, a main content area with two columns (sidebar and primary content), and a footer. Make it responsive."},
            {"name": "Build contact form with validation", "text": "Build an HTML contact form with fields for name, email, subject, and message. Include JavaScript validation to ensure all fields are filled out and the email address is in a valid format. Style it with CSS."}
        ]
        self.current_prompt_id = None
        self.associated_file_path = None # For the currently selected prompt

        self.setup_ui()
        self.load_prompts_history()

    def setup_ui(self):
        """
        Set up the prompts panel UI.
        """
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5)
        self.layout.setSpacing(5)

        # Toolbar
        self.toolbar = QToolBar("Prompts Toolbar")
        self.toolbar.setIconSize(QSize(16, 16))
        self.layout.addWidget(self.toolbar)

        self.new_prompt_action = QAction(QIcon.fromTheme("document-new", QIcon()), "New Prompt", self)
        self.new_prompt_action.triggered.connect(self.new_prompt)
        self.toolbar.addAction(self.new_prompt_action)

        self.delete_prompt_action = QAction(QIcon.fromTheme("edit-delete", QIcon()), "Delete Prompt", self)
        self.delete_prompt_action.triggered.connect(self.delete_selected_prompt)
        self.toolbar.addAction(self.delete_prompt_action)

        self.export_prompts_action = QAction(QIcon.fromTheme("document-save-as", QIcon()), "Export Prompts", self)
        self.export_prompts_action.triggered.connect(self.export_prompts)
        self.toolbar.addAction(self.export_prompts_action)

        self.import_prompts_action = QAction(QIcon.fromTheme("document-open", QIcon()), "Import Prompts", self)
        self.import_prompts_action.triggered.connect(self.import_prompts)
        self.toolbar.addAction(self.import_prompts_action)
        
        self.toolbar.addSeparator()

        self.associate_file_action = QAction(QIcon.fromTheme("document-properties", QIcon()), "Associate with File", self)
        self.associate_file_action.triggered.connect(self.associate_with_file)
        self.toolbar.addAction(self.associate_file_action)


        # Search bar
        self.search_layout = QHBoxLayout()
        self.search_label = QLabel("Search:")
        self.search_layout.addWidget(self.search_label)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search prompts...")
        self.search_input.textChanged.connect(self.filter_prompts)
        self.search_layout.addWidget(self.search_input)
        self.layout.addLayout(self.search_layout)

        # Main content area (splitter for list and editor)
        self.content_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.layout.addWidget(self.content_splitter)

        # --- Left: Prompt History List ---
        self.list_widget = QListWidget(self)
        self.list_widget.itemSelectionChanged.connect(self.on_prompt_selection_changed)
        self.list_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.list_widget.customContextMenuRequested.connect(self.show_list_context_menu)
        self.content_splitter.addWidget(self.list_widget)

        # --- Right: Prompt Editor and Details ---
        self.editor_widget = QWidget()
        self.editor_layout = QVBoxLayout(self.editor_widget)
        self.editor_layout.setContentsMargins(0, 0, 0, 0)

        # Template selector
        self.template_label = QLabel("Load Template:")
        self.editor_layout.addWidget(self.template_label)
        self.template_combo = QComboBox()
        self.template_combo.addItem("-- Select Template --")
        for template in self.prompt_templates:
            self.template_combo.addItem(template["name"], template["text"])
        self.template_combo.currentIndexChanged.connect(self.load_template)
        self.editor_layout.addWidget(self.template_combo)
        
        self.editor_layout.addWidget(QLabel("Prompt Text:"))
        self.prompt_text_edit = QTextEdit()
        self.prompt_text_edit.setPlaceholderText("Enter your AI prompt here...")
        self.prompt_text_edit.textChanged.connect(self.on_prompt_text_changed)
        self.editor_layout.addWidget(self.prompt_text_edit)

        # Associated file display
        self.associated_file_label = QLabel("Associated File: None")
        self.editor_layout.addWidget(self.associated_file_label)

        # Apply button
        self.apply_button = QPushButton("Apply Prompt (to current file)")
        self.apply_button.clicked.connect(self.apply_current_prompt)
        self.editor_layout.addWidget(self.apply_button)
        
        self.content_splitter.addWidget(self.editor_widget)
        
        # Set initial splitter sizes
        self.content_splitter.setSizes([200, 400]) # List:Editor


    def load_prompts_history(self):
        """
        Loads prompts history from the JSON file.
        """
        # Determine the path for the prompts data file
        # For now, let's assume it's in the same directory as the script or a dedicated app data dir
        # This should be refined later (e.g., per-project or global app data)
        data_dir = os.path.dirname(os.path.abspath(__file__)) # Placeholder
        actual_prompts_file_path = os.path.join(data_dir, self.prompts_data_file)

        if os.path.exists(actual_prompts_file_path):
            try:
                with open(actual_prompts_file_path, 'r', encoding='utf-8') as f:
                    self.prompts_history = json.load(f)
                self.populate_prompt_list()
            except (json.JSONDecodeError, IOError) as e:
                QMessageBox.warning(self, "Load Error", f"Could not load prompts history: {e}")
                self.prompts_history = [] # Start fresh if error
        else:
            self.prompts_history = [] # No history file yet
            self.populate_prompt_list()

    def save_prompts_history(self):
        """
        Saves prompts history to the JSON file.
        """
        data_dir = os.path.dirname(os.path.abspath(__file__)) # Placeholder
        actual_prompts_file_path = os.path.join(data_dir, self.prompts_data_file)
        try:
            with open(actual_prompts_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.prompts_history, f, indent=4, ensure_ascii=False)
        except IOError as e:
            QMessageBox.warning(self, "Save Error", f"Could not save prompts history: {e}")

    def populate_prompt_list(self, filter_text=""):
        """
        Populates the prompt list widget, optionally filtering by text.
        """
        self.list_widget.clear()
        for prompt_data in self.prompts_history:
            prompt_text = prompt_data.get('text', '')
            timestamp = prompt_data.get('timestamp', '')
            associated_file = prompt_data.get('associated_file', 'None')
            
            if filter_text.lower() not in prompt_text.lower() and \
               filter_text.lower() not in associated_file.lower():
                continue

            # Display a snippet of the prompt and timestamp
            display_text = prompt_text[:80] + "..." if len(prompt_text) > 80 else prompt_text
            list_item = QListWidgetItem(f"{timestamp[:16]} - {display_text} (File: {os.path.basename(associated_file)})")
            list_item.setData(Qt.ItemDataRole.UserRole, prompt_data) # Store the whole dict
            self.list_widget.addItem(list_item)

    def add_prompt_to_history(self, prompt_text, associated_file_path=None, tags=None):
        """
        Adds a new prompt to the history.
        """
        if not prompt_text.strip():
            return

        new_prompt = {
            "id": datetime.now().isoformat(), # Simple unique ID
            "text": prompt_text.strip(),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "associated_file": associated_file_path if associated_file_path else "None",
            "tags": tags if tags else []
        }
        self.prompts_history.insert(0, new_prompt) # Add to the beginning
        self.save_prompts_history()
        self.populate_prompt_list(self.search_input.text())
        # Optionally, select the newly added prompt
        self.list_widget.setCurrentRow(0)


    def on_prompt_selection_changed(self):
        """
        Handles selection change in the prompt list.
        """
        selected_items = self.list_widget.selectedItems()
        if selected_items:
            prompt_data = selected_items[0].data(Qt.ItemDataRole.UserRole)
            if prompt_data:
                self.current_prompt_id = prompt_data.get('id')
                self.prompt_text_edit.setPlainText(prompt_data.get('text', ''))
                self.associated_file_path = prompt_data.get('associated_file')
                self.update_associated_file_label()
                self.prompt_selected.emit(prompt_data.get('text', ''))
        else:
            self.current_prompt_id = None
            self.prompt_text_edit.clear()
            self.associated_file_path = None
            self.update_associated_file_label()

    def on_prompt_text_changed(self):
        """
        Handles text changes in the prompt editor.
        Updates the existing prompt in history or prepares to save as new.
        """
        if self.current_prompt_id:
            # Find and update the existing prompt
            for prompt in self.prompts_history:
                if prompt.get('id') == self.current_prompt_id:
                    prompt['text'] = self.prompt_text_edit.toPlainText()
                    # Update timestamp if it's a significant change? Or only on explicit save?
                    # For now, let's assume changes are auto-saved to history.
                    # prompt['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.save_prompts_history()
                    self.populate_prompt_list(self.search_input.text()) # Refresh list to show new snippet
                    # Re-select the item to keep it focused
                    items = self.list_widget.findItems(self.current_prompt_id, Qt.MatchFlag.MatchContains | Qt.MatchFlag.MatchRecursive) # This is not ideal, better to find by UserRole
                    if items:
                         self.list_widget.setCurrentItem(items[0])
                    break
        # If no current_prompt_id, it's a new prompt, not yet in history.
        # It will be added when "Apply" or "New Prompt" (as save) is clicked.

    def new_prompt(self):
        """
        Clears the editor to create a new prompt.
        """
        self.list_widget.clearSelection() # Deselect current
        self.current_prompt_id = None
        self.associated_file_path = None
        self.prompt_text_edit.clear()
        self.update_associated_file_label()
        self.prompt_text_edit.setFocus()

    def delete_selected_prompt(self):
        """
        Deletes the selected prompt from history.
        """
        selected_items = self.list_widget.selectedItems()
        if not selected_items:
            return
        
        prompt_data = selected_items[0].data(Qt.ItemDataRole.UserRole)
        if not prompt_data:
            return

        reply = QMessageBox.question(self, "Confirm Delete",
                                     "Are you sure you want to delete this prompt?",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            prompt_id_to_delete = prompt_data.get('id')
            self.prompts_history = [p for p in self.prompts_history if p.get('id') != prompt_id_to_delete]
            self.save_prompts_history()
            self.populate_prompt_list(self.search_input.text())
            if self.current_prompt_id == prompt_id_to_delete:
                self.new_prompt() # Clear editor if deleted prompt was being edited

    def apply_current_prompt(self):
        """
        Emits a signal to apply the current prompt text.
        If it's a new prompt (not in history), it's added to history first.
        """
        prompt_text = self.prompt_text_edit.toPlainText()
        if not prompt_text.strip():
            QMessageBox.information(self, "Apply Prompt", "Prompt text cannot be empty.")
            return

        if not self.current_prompt_id: # If it's a new, unsaved prompt
            self.add_prompt_to_history(prompt_text, self.associated_file_path)
            # After adding, current_prompt_id will be set by on_prompt_selection_changed if list auto-selects
            # Or we can manually get the new ID if add_prompt_to_history returns it.
            # For now, assume it gets selected or we just emit the text.

        self.prompt_to_apply.emit(prompt_text, self.associated_file_path)

    def load_template(self, index):
        """
        Loads a selected template into the prompt editor.
        """
        if index > 0: # Index 0 is the placeholder
            template_text = self.template_combo.currentData()
            if template_text:
                self.prompt_text_edit.setPlainText(template_text)
                # If this is a new prompt, it will be saved on apply or text change if an ID exists
                if not self.current_prompt_id:
                    # Consider this a new prompt based on a template
                    self.new_prompt() # Clear any existing selection
                    self.prompt_text_edit.setPlainText(template_text) # Set text after clearing

    def associate_with_file(self):
        """
        Prompts user to associate the current prompt with a file.
        """
        if not self.current_prompt_id:
            QMessageBox.information(self, "Associate File", "Please select or create a prompt first.")
            return

        # This should ideally open a file picker from the project, not a generic one.
        # For now, using a simple input dialog for the file path.
        # In a real app, this would interact with the FileTreeWidget or FileManager.
        file_path, ok = QInputDialog.getText(self, "Associate File", 
                                             "Enter file path (relative to project or absolute):",
                                             text=self.associated_file_path if self.associated_file_path else "")
        if ok and file_path:
            self.associated_file_path = file_path.strip()
            self.update_associated_file_label()
            
            # Update the prompt in history
            for prompt in self.prompts_history:
                if prompt.get('id') == self.current_prompt_id:
                    prompt['associated_file'] = self.associated_file_path
                    self.save_prompts_history()
                    self.populate_prompt_list(self.search_input.text())
                    # Re-select the item
                    items = self.list_widget.findItems(self.current_prompt_id, Qt.MatchFlag.MatchContains | Qt.MatchFlag.MatchRecursive)
                    if items:
                         self.list_widget.setCurrentItem(items[0])
                    break
            self.prompt_associated.emit(self.current_prompt_id, self.associated_file_path)


    def update_associated_file_label(self):
        """
        Updates the label showing the associated file.
        """
        if self.associated_file_path and self.associated_file_path != "None":
            self.associated_file_label.setText(f"Associated File: {os.path.basename(self.associated_file_path)}")
            self.associated_file_label.setToolTip(self.associated_file_path)
        else:
            self.associated_file_label.setText("Associated File: None")
            self.associated_file_label.setToolTip("")

    def filter_prompts(self, filter_text):
        """
        Filters the prompt list based on the search input.
        """
        self.populate_prompt_list(filter_text)

    def export_prompts(self):
        """
        Exports prompts history to a user-selected JSON file.
        """
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Prompts", "", "JSON Files (*.json)")
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.prompts_history, f, indent=4, ensure_ascii=False)
                QMessageBox.information(self, "Export Successful", f"Prompts exported to {os.path.basename(file_path)}")
            except IOError as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export prompts: {e}")

    def import_prompts(self):
        """
        Imports prompts history from a user-selected JSON file.
        """
        file_path, _ = QFileDialog.getOpenFileName(self, "Import Prompts", "", "JSON Files (*.json)")
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_prompts = json.load(f)
                
                # Merge with existing, avoiding duplicates based on ID or simple append
                # For simplicity, let's append and let the user manage duplicates.
                # A more robust solution would check for existing IDs.
                self.prompts_history.extend(imported_prompts)
                self.save_prompts_history()
                self.populate_prompt_list(self.search_input.text())
                QMessageBox.information(self, "Import Successful", f"Imported {len(imported_prompts)} prompts.")
            except (json.JSONDecodeError, IOError) as e:
                QMessageBox.critical(self, "Import Error", f"Failed to import prompts: {e}")

    def show_list_context_menu(self, position):
        """
        Shows a context menu for the prompt list.
        """
        item = self.list_widget.itemAt(position)
        if not item:
            return

        menu = QMenu(self)
        delete_action = QAction("Delete", self)
        delete_action.triggered.connect(self.delete_selected_prompt)
        
        # Add more actions like "Copy Prompt Text", "Edit Association" etc.
        
        menu.addAction(delete_action)
        menu.exec(self.list_widget.viewport().mapToGlobal(position))

    def set_associated_file_for_current_prompt(self, file_path):
        """
        Allows external components (like the main window) to set the associated file
        for the currently active prompt in the editor.
        """
        if self.current_prompt_id:
            self.associated_file_path = file_path
            self.update_associated_file_label()
            # Update in history as well
            for prompt in self.prompts_history:
                if prompt.get('id') == self.current_prompt_id:
                    prompt['associated_file'] = self.associated_file_path
                    self.save_prompts_history()
                    self.populate_prompt_list(self.search_input.text())
                    break
        # If no current_prompt_id, it means it's a new prompt.
        # We can store this association and it will be saved when the prompt is added to history.
        elif self.prompt_text_edit.toPlainText().strip(): # If there's text, it's a new prompt
             self.associated_file_path = file_path
             self.update_associated_file_label()
