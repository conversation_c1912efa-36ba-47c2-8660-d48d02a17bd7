import os
import chardet
from PyQt6.QtWidgets import QMessageBox, QFileDialog
from PyQt6.QtCore import QObject, pyqtSignal

class FileManager(QObject):
    """
    Handles file and directory operations for the application.
    """
    # Signals
    file_opened = pyqtSignal(str, str)  # file_path, content
    file_saved = pyqtSignal(str)        # file_path
    file_created = pyqtSignal(str)      # file_path
    file_deleted = pyqtSignal(str)      # file_path
    file_renamed = pyqtSignal(str, str) # old_path, new_path
    directory_created = pyqtSignal(str) # dir_path
    directory_deleted = pyqtSignal(str) # dir_path
    error_occurred = pyqtSignal(str)    # error_message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project_path = None
        self.open_files = {}  # {file_path: content}

    def set_project_path(self, path):
        """
        Sets the current project path.
        """
        if os.path.isdir(path):
            self.current_project_path = path
        else:
            self.error_occurred.emit(f"Invalid project path: {path}")

    def get_file_content(self, file_path):
        """
        Reads the content of a file, attempting to detect encoding.
        Returns the content as a string or None if an error occurs.
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                # Detect encoding
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']
                
                if encoding and confidence > 0.7:
                    return raw_data.decode(encoding)
                else:
                    # Fallback to utf-8 if detection is uncertain
                    try:
                        return raw_data.decode('utf-8')
                    except UnicodeDecodeError:
                        # Final fallback to latin-1 which rarely fails
                        return raw_data.decode('latin-1', errors='replace')
        except FileNotFoundError:
            self.error_occurred.emit(f"File not found: {file_path}")
            return None
        except IOError as e:
            self.error_occurred.emit(f"Error reading file {file_path}: {e}")
            return None
        except Exception as e:
            self.error_occurred.emit(f"An unexpected error occurred while reading {file_path}: {e}")
            return None

    def open_file(self, file_path):
        """
        Opens a file and emits its content.
        """
        content = self.get_file_content(file_path)
        if content is not None:
            self.open_files[file_path] = content
            self.file_opened.emit(file_path, content)
            return True
        return False

    def save_file(self, file_path, content):
        """
        Saves content to a file.
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.open_files[file_path] = content
            self.file_saved.emit(file_path)
            return True
        except IOError as e:
            self.error_occurred.emit(f"Error saving file {file_path}: {e}")
            return False
        except Exception as e:
            self.error_occurred.emit(f"An unexpected error occurred while saving {file_path}: {e}")
            return False

    def create_new_file(self, file_path, initial_content=""):
        """
        Creates a new file with optional initial content.
        """
        if os.path.exists(file_path):
            self.error_occurred.emit(f"File already exists: {file_path}")
            return False
        
        if self.save_file(file_path, initial_content):
            self.file_created.emit(file_path)
            return True
        return False

    def delete_file(self, file_path):
        """
        Deletes a file.
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                if file_path in self.open_files:
                    del self.open_files[file_path]
                self.file_deleted.emit(file_path)
                return True
            else:
                self.error_occurred.emit(f"File not found: {file_path}")
                return False
        except OSError as e:
            self.error_occurred.emit(f"Error deleting file {file_path}: {e}")
            return False

    def rename_file(self, old_path, new_path):
        """
        Renames or moves a file.
        """
        try:
            if not os.path.exists(old_path):
                self.error_occurred.emit(f"File not found: {old_path}")
                return False
            if os.path.exists(new_path):
                self.error_occurred.emit(f"Target file already exists: {new_path}")
                return False
            
            os.rename(old_path, new_path)
            if old_path in self.open_files:
                self.open_files[new_path] = self.open_files.pop(old_path)
            self.file_renamed.emit(old_path, new_path)
            return True
        except OSError as e:
            self.error_occurred.emit(f"Error renaming file {old_path} to {new_path}: {e}")
            return False

    def create_directory(self, dir_path):
        """
        Creates a new directory.
        """
        try:
            if os.path.exists(dir_path):
                self.error_occurred.emit(f"Directory already exists: {dir_path}")
                return False
            os.makedirs(dir_path)
            self.directory_created.emit(dir_path)
            return True
        except OSError as e:
            self.error_occurred.emit(f"Error creating directory {dir_path}: {e}")
            return False

    def delete_directory(self, dir_path):
        """
        Deletes a directory and its contents.
        Use with caution.
        """
        try:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                # Remove files from open_files cache if they are in this directory
                files_to_remove = [fp for fp in self.open_files if fp.startswith(dir_path)]
                for f_path in files_to_remove:
                    del self.open_files[f_path]
                
                import shutil
                shutil.rmtree(dir_path)
                self.directory_deleted.emit(dir_path)
                return True
            else:
                self.error_occurred.emit(f"Directory not found: {dir_path}")
                return False
        except OSError as e:
            self.error_occurred.emit(f"Error deleting directory {dir_path}: {e}")
            return False

    def list_directory(self, dir_path, show_hidden=False):
        """
        Lists files and directories in a given path.
        Returns a list of dictionaries with 'name', 'path', 'is_dir', 'is_file'.
        """
        items = []
        try:
            if not os.path.isdir(dir_path):
                self.error_occurred.emit(f"Not a directory: {dir_path}")
                return items
            
            for entry in os.scandir(dir_path):
                if not show_hidden and entry.name.startswith('.'):
                    continue
                items.append({
                    "name": entry.name,
                    "path": entry.path,
                    "is_dir": entry.is_dir(),
                    "is_file": entry.is_file()
                })
            return sorted(items, key=lambda x: (not x['is_dir'], x['name'].lower()))
        except OSError as e:
            self.error_occurred.emit(f"Error listing directory {dir_path}: {e}")
            return []

    def get_relative_path(self, absolute_path):
        """
        Converts an absolute path to a path relative to the current project.
        Returns the original path if no project is set or if it's not within the project.
        """
        if self.current_project_path and absolute_path.startswith(self.current_project_path):
            return os.path.relpath(absolute_path, self.current_project_path)
        return absolute_path

    def get_absolute_path(self, relative_path):
        """
        Converts a relative path (to project) to an absolute path.
        Returns the original path if no project is set.
        """
        if self.current_project_path and not os.path.isabs(relative_path):
            return os.path.join(self.current_project_path, relative_path)
        return relative_path

    def is_valid_project_file(self, file_path):
        """
        Checks if a file is relevant for the project (e.g., HTML, CSS, JS).
        """
        valid_extensions = {'.html', '.htm', '.css', '.js', '.json', '.md', '.txt', '.xml', '.svg'}
        return os.path.splitext(file_path)[1].lower() in valid_extensions

    def close_all_files(self):
        """
        Clears the list of open files.
        """
        self.open_files.clear()

    def get_open_file_content(self, file_path):
        """
        Returns the cached content of an open file.
        """
        return self.open_files.get(file_path)

    def is_file_open(self, file_path):
        """
        Checks if a file is currently open (cached).
        """
        return file_path in self.open_files
