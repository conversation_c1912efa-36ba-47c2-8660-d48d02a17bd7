import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow

def main():
    """
    Main entry point for the HTML Dev Studio application.
    """
    app = QApplication(sys.argv)
    
    # Set application organization and name for settings
    app.setOrganizationName("VibeCoding")
    app.setApplicationName("HTML Dev Studio")
    app.setApplicationVersion("1.0.0")
    
    # Enable High DPI display with PyQt6
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
    
    # Create and show the main window
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
