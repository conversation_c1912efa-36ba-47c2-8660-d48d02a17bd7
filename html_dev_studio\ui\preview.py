import os
import tempfile
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QToolBar, 
                             QComboBox, QLabel, QSizePolicy)
from PyQt6.QtCore import Qt, QUrl, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QAction
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile

class PreviewEngine(QWidget):
    """
    A widget that provides a live HTML preview using QWebEngineView.
    """
    # Signals
    title_changed = pyqtSignal(str) # Title of the loaded page
    url_changed = pyqtSignal(QUrl) # URL of the loaded page

    def __init__(self, file_manager, parent=None):
        super().__init__(parent)
        self.file_manager = file_manager
        self.current_html_path = None
        self.current_project_path = None # Needed for resolving relative paths
        self.auto_refresh_timer = QTimer(self)
        self.auto_refresh_timer.setSingleShot(True)
        self.auto_refresh_timer.timeout.connect(self.refresh_preview)

        self.setup_ui()
        self.setup_web_settings()

    def setup_ui(self):
        """
        Set up the preview UI.
        """
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # Toolbar for preview controls
        self.toolbar = QToolBar("Preview Toolbar")
        self.toolbar.setIconSize(Qt.QSize(16, 16))
        self.layout.addWidget(self.toolbar)

        # Viewport size selector
        self.viewport_label = QLabel("Viewport:")
        self.toolbar.addWidget(self.viewport_label)
        
        self.viewport_combo = QComboBox()
        self.viewport_combo.addItems([
            "Desktop (1920x1080)", 
            "Tablet (768x1024)", 
            "Mobile (375x667)",
            "Custom"
        ])
        self.viewport_combo.currentTextChanged.connect(self.on_viewport_changed)
        self.toolbar.addWidget(self.viewport_combo)
        
        self.toolbar.addSeparator()

        # Refresh action
        self.refresh_action = QAction(QIcon.fromTheme("view-refresh", QIcon()), "Refresh", self)
        self.refresh_action.triggered.connect(self.refresh_preview)
        self.toolbar.addAction(self.refresh_action)

        # Developer tools action
        self.dev_tools_action = QAction(QIcon.fromTheme("tools-report-bug", QIcon()), "Developer Tools", self)
        self.dev_tools_action.triggered.connect(self.open_dev_tools)
        self.toolbar.addAction(self.dev_tools_action)
        
        self.toolbar.addSeparator()
        
        # Auto-refresh toggle
        self.auto_refresh_action = QAction("Auto-refresh", self)
        self.auto_refresh_action.setCheckable(True)
        self.auto_refresh_action.setChecked(True) # Enabled by default
        self.toolbar.addAction(self.auto_refresh_action)


        # WebEngineView
        self.web_view = QWebEngineView(self)
        self.web_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.web_view.titleChanged.connect(self.title_changed.emit)
        self.web_view.urlChanged.connect(self.url_changed.emit)
        
        # Handle page load finished for any post-load actions
        self.web_view.loadFinished.connect(self.on_page_load_finished)

        self.layout.addWidget(self.web_view)

    def setup_web_settings(self):
        """
        Configure QWebEngineSettings for the preview.
        """
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.AllowGeolocationOnInsecureOrigins, True) # If needed
        settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True) # If needed
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.PluginsEnabled, False) # Usually not needed for basic preview
        settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)
        
        # Set a user agent string if needed
        # settings.setUserAgentString("CustomUserAgent/1.0")

    def set_project_path(self, path):
        """
        Sets the current project path, which is needed to resolve relative paths for CSS/JS.
        """
        self.current_project_path = path

    def load_html(self, html_content, base_path=None):
        """
        Loads HTML content into the preview.
        - html_content: The HTML string to load.
        - base_path: The directory of the HTML file, used to resolve relative paths.
                     If None, uses current_project_path.
        """
        if not self.current_project_path and not base_path:
            self.web_view.setHtml("<html><body><p>Error: No project path set. Cannot resolve relative paths for CSS/JS.</p></body></html>")
            return

        # Determine the base URL for resolving relative paths
        # QWebEngineView needs a file URL or a proper base to resolve relative links correctly.
        # A common trick is to use a temporary file or a custom scheme.
        # Here, we'll use a temporary file to ensure a proper file URL context.
        
        effective_base_path = base_path if base_path else self.current_project_path
        
        try:
            # Create a temporary HTML file to serve as the base URL
            # This helps QWebEngine resolve relative paths for CSS and JS correctly.
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.html', dir=effective_base_path) as tmp_file:
                tmp_file.write(html_content)
                temp_file_path = tmp_file.name
            
            self.current_html_path = temp_file_path # Store to clean up later if needed
            
            # Load the temporary file
            file_url = QUrl.fromLocalFile(temp_file_path)
            self.web_view.load(file_url)

        except Exception as e:
            error_html = f"<html><body><p>Error creating temporary file for preview: {e}</p></body></html>"
            self.web_view.setHtml(error_html)
            self.current_html_path = None


    def load_html_file(self, file_path):
        """
        Loads an HTML file from the given path into the preview.
        """
        if not os.path.exists(file_path):
            self.web_view.setHtml(f"<html><body><p>Error: File not found - {file_path}</p></body></html>")
            self.current_html_path = None
            return

        content = self.file_manager.get_file_content(file_path)
        if content is not None:
            # The base_path for resolving relative paths is the directory of the HTML file itself.
            base_path = os.path.dirname(file_path)
            self.load_html(content, base_path)
        else:
            self.web_view.setHtml(f"<html><body><p>Error: Could not read file - {file_path}</p></body></html>")
            self.current_html_path = None

    def refresh_preview(self):
        """
        Refreshes the current preview.
        If an HTML file was loaded, it reloads it.
        If HTML content was loaded directly, it re-loads that content.
        """
        if self.current_html_path and os.path.exists(self.current_html_path):
            # If it was loaded from a temp file (which means it was from content)
            # We need to re-read the original source if it was from an open editor tab
            # This logic needs to be connected to the editor's content.
            # For now, just reload the URL.
            self.web_view.reload()
        else:
            # Fallback or if loaded directly via setHtml (less common for our use case)
            self.web_view.reload()


    def schedule_refresh(self):
        """
        Schedules a preview refresh if auto-refresh is enabled.
        Debounces rapid changes.
        """
        if self.auto_refresh_action.isChecked():
            self.auto_refresh_timer.start(500) # 500ms debounce

    def on_viewport_changed(self, text):
        """
        Handles changes in the viewport size selector.
        """
        if text == "Desktop (1920x1080)":
            self.web_view.setFixedSize(1920, 1080) # Or use a scaling factor
        elif text == "Tablet (768x1024)":
            self.web_view.setFixedSize(768, 1024)
        elif text == "Mobile (375x667)":
            self.web_view.setFixedSize(375, 667)
        elif text == "Custom":
            # TODO: Implement custom viewport size dialog
            pass
        # Reset size policy if needed, or let it be managed by splitter
        self.web_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)


    def open_dev_tools(self):
        """
        Opens the developer tools for the current page.
        """
        # Create a new QWebEngineView for dev tools if it doesn't exist
        # Or, more commonly, use inspectElement() which might open it in a new window
        # or a dockable widget depending on the platform and Qt version.
        # For simplicity, we'll try to open it in a new window.
        page = self.web_view.page()
        if page:
            # This might open it in a new window or a side panel.
            # The exact behavior can vary.
            page.triggerAction(QWebEnginePage.WebAction.InspectElement)
            
            # Alternative: Create a dedicated DevTools window
            # from PyQt6.QtWebEngineWidgets import QWebEngineView
            # dev_tools_view = QWebEngineView()
            # page.setDevToolsPage(dev_tools_view.page())
            # dev_tools_view.show()

    def on_page_load_finished(self, ok):
        """
        Called when a page load is finished.
        'ok' is True if the load was successful.
        """
        if not ok:
            # Optionally, display an error message in the preview or status bar
            error_html = "<html><body><p style='color:red;'>Failed to load page.</p></body></html>"
            # self.web_view.setHtml(error_html) # This might loop, use with caution
            print("Preview page failed to load.") # Log to console for now

    def clear_preview(self):
        """
        Clears the preview pane.
        """
        self.web_view.setHtml("<html><body><p>Preview will appear here.</p><p>Open an HTML file to begin.</p></body></html>")
        self.current_html_path = None
        if self.auto_refresh_timer.isActive():
            self.auto_refresh_timer.stop()

    def cleanup_temp_file(self):
        """
        Cleans up the temporary HTML file if one was created.
        """
        if self.current_html_path and os.path.exists(self.current_html_path):
            try:
                os.remove(self.current_html_path)
            except OSError as e:
                print(f"Error removing temporary file {self.current_html_path}: {e}")
        self.current_html_path = None

    def closeEvent(self, event):
        """
        Handles the close event for the widget.
        """
        self.cleanup_temp_file()
        super().closeEvent(event)

    # --- Stubs for methods called by MainWindow ---
    def set_project_path(self, path):
        """
        Sets the current project path, which is needed to resolve relative paths for CSS/JS.
        This method was already defined above, but as a stub for completeness if it was missing.
        """
        self.current_project_path = path

    def cleanup_temp_file(self):
        """
        Cleans up the temporary HTML file if one was created.
        This method was already defined above.
        """
        if self.current_html_path and os.path.exists(self.current_html_path):
            try:
                os.remove(self.current_html_path)
            except OSError as e:
                print(f"Error removing temporary file {self.current_html_path}: {e}")
        self.current_html_path = None

# Required imports for QWebEngineView and settings
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEnginePage, QWebEngineProfile
