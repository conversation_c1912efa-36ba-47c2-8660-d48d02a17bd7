# HTML Dev Studio

A modern, professional Python desktop application for HTML/CSS/JavaScript development with live preview, syntax highlighting, and AI prompt management.

## Features

### ✅ Core Features (Implemented)
- **Project Management**: Open and manage web development projects
- **File Explorer**: Hierarchical file tree with context menu operations
- **Multi-tab Code Editor**: 
  - Syntax highlighting for HTML, CSS, JavaScript
  - Line numbers
  - Auto-save functionality
  - Dirty file indicators
- **Live HTML Preview**: Real-time preview using QWebEngineView
- **AI Prompts Panel**: Manage and organize development prompts
- **Professional UI**: Modern interface with resizable panels

### 🚧 Planned Features
- Find/Replace functionality
- Auto-completion for HTML/CSS/JavaScript
- Code folding
- Theme support (light/dark)
- File watching for external changes
- HTML/CSS validation
- Export project functionality

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Dependencies
Install the required dependencies:

```bash
cd html_dev_studio
pip install -r requirements.txt
```

Required packages:
- PyQt6 >= 6.4.0
- PyQt6-WebEngine >= 6.4.0
- Pygments >= 2.14.0
- watchdog >= 3.0.0
- chardet >= 5.1.0

## Usage

### Starting the Application
```bash
cd html_dev_studio
python main.py
```

### Basic Workflow
1. **Open a Project**: File → Open Project (or Ctrl+O)
2. **Navigate Files**: Use the file tree on the left to browse your project
3. **Edit Code**: Double-click files to open them in the editor
4. **Live Preview**: HTML files automatically appear in the preview panel
5. **Manage Prompts**: Use the bottom panel to store and organize AI prompts

### Keyboard Shortcuts
- `Ctrl+O`: Open Project
- `Ctrl+S`: Save Current File
- `Ctrl+Shift+S`: Save All Files
- `Ctrl+Shift+N`: New Project
- `Alt+F4`: Exit Application

## Project Structure

```
html_dev_studio/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── ui/                     # User interface modules
│   ├── main_window.py     # Main application window
│   ├── editor.py          # Code editor with syntax highlighting
│   ├── preview.py         # HTML preview engine
│   ├── file_tree.py       # Project file explorer
│   └── prompts_panel.py   # AI prompts management
├── core/                   # Core functionality
│   └── file_manager.py    # File operations handler
└── test_dependencies.py   # Dependency verification script
```

## Testing

A test project is included in the `test_project/` directory with sample HTML, CSS, and JavaScript files to verify functionality.

To test dependencies:
```bash
python test_dependencies.py
```

## Development

### Architecture
- **PyQt6**: Modern GUI framework with native look and feel
- **QWebEngineView**: Chromium-based HTML preview
- **Signal/Slot Pattern**: Clean component communication
- **Modular Design**: Separated concerns for maintainability

### Key Classes
- `MainWindow`: Central application coordinator
- `FileManager`: Handles all file operations with error handling
- `CodeEditor`: Enhanced text editor with syntax highlighting
- `PreviewEngine`: Live HTML preview with auto-refresh
- `FileTreeWidget`: Project navigation and file management
- `PromptsPanel`: AI prompt storage and organization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source. See LICENSE file for details.

## Support

For issues, feature requests, or questions:
1. Check the existing issues
2. Create a new issue with detailed description
3. Include system information and error messages

## Changelog

### Version 1.0.0 (Current)
- Initial release with core functionality
- Project management and file operations
- Code editor with syntax highlighting
- Live HTML preview
- AI prompts management
- Professional UI with resizable panels
