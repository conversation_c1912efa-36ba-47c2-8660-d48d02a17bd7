import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTabWidget, QTabBar, 
                             QTextEdit, QMessageBox, QFileDialog, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QIcon, QTextCursor, QColor, QSyntaxHighlighter, QTextCharFormat, QPalette

# Placeholder for FileManager
# from core.file_manager import FileManager

class LineNumberArea(QWidget):
    """
    A widget for displaying line numbers in a code editor.
    """
    def __init__(self, editor):
        super().__init__(editor)
        self.code_editor = editor
        self.setMouseTracking(True)

    def sizeHint(self):
        return QSize(self.code_editor.line_number_area_width(), 0)

    def paintEvent(self, event):
        self.code_editor.line_number_area_paint_event(event)

class CodeEditor(QTextEdit):
    """
    A custom text editor with line numbers and basic syntax highlighting.
    """
    # Signals
    text_changed = pyqtSignal(str) # file_path
    file_saved = pyqtSignal(str)   # file_path
    file_closed = pyqtSignal(str)  # file_path

    def __init__(self, file_path, file_manager, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.file_manager = file_manager
        self.is_dirty = False

        self.line_number_area = LineNumberArea(self)
        
        self.blockCountChanged.connect(self.update_line_number_area_width)
        self.updateRequest.connect(self.update_line_number_area)
        self.textChanged.connect(self.on_text_changed)

        self.update_line_number_area_width(0)
        self.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)
        
        # Basic font
        font = self.font()
        font.setFamily("Consolas" if os.name == 'nt' else "Monospace")
        font.setPointSize(10)
        self.setFont(font)
        
        # Apply basic syntax highlighter
        self.highlighter = SyntaxHighlighter(self.document())
        self.highlighter.set_document_language(self.file_path)

    def line_number_area_width(self):
        """
        Calculates the width of the line number area.
        """
        digits = 1
        max_value = max(1, self.blockCount())
        while max_value >= 10:
            max_value /= 10
            digits += 1
        space = 3 + self.fontMetrics().horizontalAdvance('9') * digits
        return space

    def update_line_number_area_width(self, new_block_count):
        """
        Updates the viewport margins to accommodate the line number area.
        """
        self.setViewportMargins(self.line_number_area_width(), 0, 0, 0)

    def update_line_number_area(self, rect, dy):
        """
        Updates the line number area when the editor is scrolled or updated.
        """
        if dy:
            self.line_number_area.scroll(0, dy)
        else:
            self.line_number_area.update(0, rect.y(), self.line_number_area.width(), rect.height())

        if rect.contains(self.viewport().rect()):
            self.update_line_number_area_width(0)

    def resizeEvent(self, event):
        """
        Handles resize events for the editor.
        """
        super().resizeEvent(event)
        cr = self.contentsRect()
        self.line_number_area.setGeometry(QRect(cr.left(), cr.top(), self.line_number_area_width(), cr.height()))

    def line_number_area_paint_event(self, event):
        """
        Paints the line numbers.
        """
        painter = QPainter(self.line_number_area)
        painter.fillRect(event.rect(), Qt.GlobalColor.lightGray)

        block = self.firstVisibleBlock()
        block_number = block.blockNumber()
        top = self.blockBoundingGeometry(block).translated(self.contentOffset()).top()
        bottom = top + self.blockBoundingRect(block).height()

        while block.isValid() and top <= event.rect().bottom():
            if block.isVisible() and bottom >= event.rect().top():
                number = str(block_number + 1)
                painter.setPen(Qt.GlobalColor.black)
                painter.drawText(0, int(top), self.line_number_area.width() - 5, self.fontMetrics().height(),
                                 Qt.AlignmentFlag.AlignRight, number)
            block = block.next()
            top = bottom
            bottom = top + self.blockBoundingRect(block).height()
            block_number += 1

    def on_text_changed(self):
        """
        Handles text changes in the editor.
        """
        if not self.is_dirty:
            self.is_dirty = True
            # Update tab title to show dirty state (handled by CodeEditorTabWidget)
            self.text_changed.emit(self.file_path)

    def load_content(self, content):
        """
        Loads content into the editor.
        """
        self.setPlainText(content)
        self.is_dirty = False
        self.text_changed.emit(self.file_path) # To update tab title

    def get_content(self):
        """
        Returns the current content of the editor.
        """
        return self.toPlainText()

    def save_file(self):
        """
        Saves the current content to the file.
        """
        if self.file_manager.save_file(self.file_path, self.get_content()):
            self.is_dirty = False
            self.file_saved.emit(self.file_path)
            self.text_changed.emit(self.file_path) # To update tab title
            return True
        return False

    def close_file(self):
        """
        Closes the file, prompting to save if dirty.
        """
        if self.is_dirty:
            reply = QMessageBox.question(self, "Unsaved Changes",
                                         f"'{os.path.basename(self.file_path)}' has unsaved changes. Save before closing?",
                                         QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel,
                                         QMessageBox.StandardButton.Cancel)
            if reply == QMessageBox.StandardButton.Yes:
                if not self.save_file():
                    return False # Save failed or was cancelled
            elif reply == QMessageBox.StandardButton.Cancel:
                return False # User cancelled closing
        
        self.file_closed.emit(self.file_path)
        return True

    def set_language(self, language):
        """
        Sets the syntax highlighting language.
        """
        if self.highlighter:
            self.highlighter.set_document_language(self.file_path, language)


class SyntaxHighlighter(QSyntaxHighlighter):
    """
    Basic syntax highlighter for HTML, CSS, and JavaScript.
    """
    def __init__(self, document):
        super().__init__(document)
        self.rules = []
        self.setup_html_rules() # Default to HTML

    def set_document_language(self, file_path, language=None):
        """
        Sets up highlighting rules based on file extension or explicit language.
        """
        self.rules = []
        if language:
            lang = language.lower()
        else:
            lang = os.path.splitext(file_path)[1].lower()

        if lang in {'.html', '.htm'}:
            self.setup_html_rules()
        elif lang == '.css':
            self.setup_css_rules()
        elif lang == '.js':
            self.setup_js_rules()
        else:
            # Default to plain text (no rules)
            pass
        self.rehighlight()

    def setup_html_rules(self):
        """
        Sets up syntax highlighting rules for HTML.
        """
        # TextCharFormat for different elements
        tag_format = QTextCharFormat()
        tag_format.setForeground(QColor("#0000FF")) # Blue for tags
        tag_format.setFontWeight(QFont.Weight.Bold)
        self.rules.append((r"<[^>]+>", tag_format)) # Tags

        attr_format = QTextCharFormat()
        attr_format.setForeground(QColor("#FF0000")) # Red for attributes
        self.rules.append((r"\b[a-zA-Z_][a-zA-Z0-9_]*\s*=", attr_format)) # Attributes

        string_format = QTextCharFormat()
        string_format.setForeground(QColor("#008000")) # Green for strings
        self.rules.append((r"\"[^\"]*\"", string_format)) # Double quoted strings
        self.rules.append((r"'[^']*'", string_format))   # Single quoted strings

        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor("#808080")) # Grey for comments
        comment_format.setFontItalic(True)
        self.rules.append((r"<!--.*?-->", comment_format)) # HTML comments

    def setup_css_rules(self):
        """
        Sets up syntax highlighting rules for CSS.
        """
        selector_format = QTextCharFormat()
        selector_format.setForeground(QColor("#800080")) # Purple for selectors
        self.rules.append((r"[a-zA-Z0-9_#\.\-\s\*]+{", selector_format)) # Selectors

        property_format = QTextCharFormat()
        property_format.setForeground(QColor("#FF0000")) # Red for properties
        self.rules.append((r"\b[a-zA-Z-]+\s*:", property_format)) # Properties

        value_format = QTextCharFormat()
        value_format.setForeground(QColor("#0000FF")) # Blue for values
        self.rules.append((r":[^;]+;", value_format)) # Values (simplified)

        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor("#808080")) # Grey for comments
        comment_format.setFontItalic(True)
        self.rules.append((r"/\*.*?\*/", comment_format)) # CSS comments

    def setup_js_rules(self):
        """
        Sets up syntax highlighting rules for JavaScript.
        """
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(QColor("#0000FF")) # Blue for keywords
        keyword_format.setFontWeight(QFont.Weight.Bold)
        keywords = r"\b(var|let|const|function|return|if|else|for|while|do|switch|case|break|continue|try|catch|finally|class|extends|super|import|export|default|async|await|true|false|null|undefined|this|new|delete|typeof|instanceof|in|of)\b"
        self.rules.append((keywords, keyword_format))

        string_format = QTextCharFormat()
        string_format.setForeground(QColor("#008000")) # Green for strings
        self.rules.append((r"\"[^\"]*\"", string_format))
        self.rules.append((r"'[^']*'", string_format))
        self.rules.append((r"`[^`]*`", string_format)) # Template literals

        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor("#808080")) # Grey for comments
        comment_format.setFontItalic(True)
        self.rules.append((r"//.*$", comment_format))   # Single line comments
        self.rules.append((r"/\*.*?\*/", comment_format)) # Multi-line comments

        number_format = QTextCharFormat()
        number_format.setForeground(QColor("#FF00FF")) # Magenta for numbers
        self.rules.append((r"\b[0-9]+\b", number_format))


    def highlightBlock(self, text):
        """
        Applies syntax highlighting to a block of text.
        """
        for pattern, format in self.rules:
            for match in re.finditer(pattern, text, re.DOTALL): # re.DOTALL for multi-line comments
                self.setFormat(match.start(), match.end() - match.start(), format)


class CodeEditorTabWidget(QTabWidget):
    """
    A tabbed widget for managing multiple CodeEditor instances.
    """
    # Signals
    current_editor_changed = pyqtSignal(str) # file_path of current tab
    editor_text_changed = pyqtSignal(str)   # file_path of tab whose text changed
    editor_file_saved = pyqtSignal(str)     # file_path of tab that was saved
    editor_file_closed = pyqtSignal(str)    # file_path of tab that was closed
    save_all_requested = pyqtSignal()       # Request to save all open files

    def __init__(self, file_manager, parent=None):
        super().__init__(parent)
        self.file_manager = file_manager
        self.open_editors = {}  # {file_path: CodeEditor instance}
        self.setTabsClosable(True)
        self.setMovable(True)
        
        # Custom tab bar to show dirty state
        self.tab_bar = TabBar(self)
        self.setTabBar(self.tab_bar)

        self.tabCloseRequested.connect(self.close_tab)
        self.currentChanged.connect(self.on_current_tab_changed)

    def add_editor_for_file(self, file_path, content=None):
        """
        Adds a new tab for a file. If content is None, it's loaded from the file.
        """
        if file_path in self.open_editors:
            self.setCurrentWidget(self.open_editors[file_path])
            return

        if content is None:
            content = self.file_manager.get_file_content(file_path)
            if content is None:
                self.file_manager.error_occurred.emit(f"Could not load content for {file_path}")
                return
        
        editor = CodeEditor(file_path, self.file_manager, self)
        editor.load_content(content)
        
        index = self.addTab(editor, os.path.basename(file_path))
        self.setTabToolTip(index, file_path)
        self.open_editors[file_path] = editor
        
        # Connect editor signals
        editor.text_changed.connect(self.on_editor_text_changed)
        editor.file_saved.connect(self.on_editor_file_saved)
        editor.file_closed.connect(self.on_editor_file_closed)
        
        self.setCurrentWidget(editor)
        self.current_editor_changed.emit(file_path)

    def close_tab(self, index):
        """
        Closes the tab at the given index.
        """
        editor = self.widget(index)
        if editor and isinstance(editor, CodeEditor):
            if editor.close_file(): # This will prompt to save if dirty
                file_path = editor.file_path
                self.removeTab(index)
                if file_path in self.open_editors:
                    del self.open_editors[file_path]
                editor.deleteLater() # Clean up the editor widget
            # If editor.close_file() returns False, user cancelled or save failed.

    def close_all_tabs(self):
        """
        Attempts to close all open tabs.
        """
        # Iterate backwards because indices change as tabs are removed
        for i in range(self.count() - 1, -1, -1):
            self.close_tab(i)

    def get_current_editor(self):
        """
        Returns the currently active CodeEditor instance.
        """
        current_widget = self.currentWidget()
        if isinstance(current_widget, CodeEditor):
            return current_widget
        return None

    def get_current_file_path(self):
        """
        Returns the file path of the currently active tab.
        """
        editor = self.get_current_editor()
        return editor.file_path if editor else None

    def save_current_file(self):
        """
        Saves the file in the currently active tab.
        """
        editor = self.get_current_editor()
        if editor:
            return editor.save_file()
        return False

    def save_all_files(self):
        """
        Saves all open files that are dirty.
        """
        all_saved = True
        for editor in self.open_editors.values():
            if editor.is_dirty:
                if not editor.save_file():
                    all_saved = False
        return all_saved

    def on_current_tab_changed(self, index):
        """
        Handles the event when the current tab changes.
        """
        editor = self.widget(index)
        if isinstance(editor, CodeEditor):
            self.current_editor_changed.emit(editor.file_path)
        else:
            self.current_editor_changed.emit(None)

    def on_editor_text_changed(self, file_path):
        """
        Handles text changed signal from an editor.
        """
        if file_path in self.open_editors:
            editor = self.open_editors[file_path]
            tab_index = self.indexOf(editor)
            if tab_index != -1:
                title = os.path.basename(file_path)
                if editor.is_dirty:
                    title += " *"
                self.setTabText(tab_index, title)
        self.editor_text_changed.emit(file_path)

    def on_editor_file_saved(self, file_path):
        """
        Handles file saved signal from an editor.
        """
        if file_path in self.open_editors:
            editor = self.open_editors[file_path]
            tab_index = self.indexOf(editor)
            if tab_index != -1:
                self.setTabText(tab_index, os.path.basename(file_path)) # Remove asterisk
        self.editor_file_saved.emit(file_path)

    def on_editor_file_closed(self, file_path):
        """
        Handles file closed signal from an editor (already handled by close_tab mostly).
        """
        self.editor_file_closed.emit(file_path)

    def is_file_open(self, file_path):
        """
        Checks if a file is already open in a tab.
        """
        return file_path in self.open_editors

    def set_line_numbers_visible(self, visible):
        """
        Toggles the visibility of line numbers for all editors.
        """
        for editor in self.open_editors.values():
            if visible:
                editor.line_number_area.show()
            else:
                editor.line_number_area.hide()
            editor.update_line_number_area_width(0) # Recalculate margins

    # --- Stubs for methods called by MainWindow ---
    def update_file_path_in_tab(self, old_path, new_path):
        """Updates the file path associated with a tab."""
        if old_path in self.open_editors:
            editor = self.open_editors.pop(old_path)
            self.open_editors[new_path] = editor
            tab_index = self.indexOf(editor)
            if tab_index != -1:
                self.setTabText(tab_index, os.path.basename(new_path))
                self.setTabToolTip(tab_index, new_path)
            # Update editor's internal path if it stores one
            if hasattr(editor, 'file_path'):
                editor.file_path = new_path
            self.editor_file_renamed.emit(old_path, new_path) # Emit a signal if needed

    def close_files_in_directory(self, dir_path):
        """Closes all files that are within the specified directory."""
        files_to_close = [fp for fp in self.open_editors if fp.startswith(dir_path)]
        for file_path in files_to_close:
            self.close_file(file_path)

    def get_current_open_html_file(self):
        """Returns the file path of the currently open HTML file, if any."""
        current_editor = self.currentWidget()
        if current_editor and hasattr(current_editor, 'file_path'):
            path = current_editor.file_path
            if path and path.lower().endswith(('.html', '.htm')):
                return path
        return None
        
    def has_dirty_files(self):
        """Checks if any open file has unsaved changes."""
        for editor in self.open_editors.values():
            if hasattr(editor, 'document') and editor.document().isModified():
                return True
        return False

    def is_current_editor_dirty(self):
        """Checks if the currently active editor has unsaved changes."""
        current_editor = self.currentWidget()
        if current_editor and hasattr(current_editor, 'document'):
            return current_editor.document().isModified()
        return False
        
    def save_current_file(self):
        """Saves the currently active file."""
        current_editor = self.currentWidget()
        if current_editor and hasattr(current_editor, 'file_path') and current_editor.file_path:
            content = current_editor.toPlainText()
            # This should ideally trigger a signal that MainWindow connects to FileManager
            # For now, a direct call or a more robust save mechanism is needed.
            # This is a placeholder for a more complete save logic.
            self.file_manager.save_file(current_editor.file_path, content)
            current_editor.document().setModified(False)
            self.update_tab_title(current_editor.file_path, False) # Remove asterisk
        else:
            # Handle "Save As" if file_path is not set
            pass


    def save_all_files(self):
        """Saves all open, modified files."""
        for file_path, editor in self.open_editors.items():
            if editor.document().isModified():
                content = editor.toPlainText()
                self.file_manager.save_file(file_path, content)
                editor.document().setModified(False)
                self.update_tab_title(file_path, False) # Remove asterisk

    def switch_to_file(self, file_path):
        """Switches focus to the tab containing the specified file."""
        if file_path in self.open_editors:
            editor = self.open_editors[file_path]
            self.setCurrentWidget(editor)


class TabBar(QTabBar):
    """
    Custom TabBar to potentially add more features if needed.
    For now, it's a standard QTabBar.
    """
    def __init__(self, parent=None):
        super().__init__(parent)

# Required imports for SyntaxHighlighter and CodeEditor
import re
from PyQt6.QtGui import QPainter, QTextDocument, QFont
from PyQt6.QtCore import QRect
