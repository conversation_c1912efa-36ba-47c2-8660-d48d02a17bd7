import os
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QSplitter, QMenuBar, QMenu, QStatusBar, QToolBar,
                             QFileDialog, QMessageBox, QTabWidget)
from PyQt6.QtGui import QIcon, QAction
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QSettings

from core.file_manager import FileManager
from ui.file_tree import FileTreeWidget
from ui.editor import CodeEditorTabWidget
from ui.preview import PreviewEngine
from ui.prompts_panel import PromptsPanel

class MainWindow(QMainWindow):
    """
    Main application window for HTML Dev Studio.
    """
    def __init__(self):
        super().__init__()
        self.setObjectName("MainWindow")
        self.setWindowTitle("HTML Dev Studio")
        self.setGeometry(100, 100, 1200, 800) # Default size and position

        self.current_project_path = None
        self.file_manager = FileManager(self) # Instantiate FileManager
        
        self.setup_ui()
        self.create_actions()
        self.create_menus()
        self.create_toolbars()
        self.create_status_bar()
        self.connect_signals() # Connect all widget signals
        self.load_settings()

    def connect_signals(self):
        """
        Connects signals from various UI components and the FileManager.
        """
        # FileManager signals
        self.file_manager.error_occurred.connect(self.on_file_manager_error)
        self.file_manager.file_opened.connect(self.on_file_opened_by_manager)
        self.file_manager.file_saved.connect(self.on_file_saved_by_manager)
        self.file_manager.file_created.connect(self.on_file_created_by_manager)
        self.file_manager.file_deleted.connect(self.on_file_deleted_by_manager)
        self.file_manager.file_renamed.connect(self.on_file_renamed_by_manager)
        self.file_manager.directory_created.connect(self.on_directory_created_by_manager)
        self.file_manager.directory_deleted.connect(self.on_directory_deleted_by_manager)

        # FileTreeWidget signals
        self.file_tree_widget.file_open_requested.connect(self.open_file_in_editor)
        self.file_tree_widget.file_create_requested.connect(self.create_new_file_in_project)
        self.file_tree_widget.file_delete_requested.connect(self.delete_file_in_project)
        self.file_tree_widget.file_rename_requested.connect(self.rename_file_in_project)
        self.file_tree_widget.directory_create_requested.connect(self.create_new_directory_in_project)
        self.file_tree_widget.directory_delete_requested.connect(self.delete_directory_in_project)
        self.file_tree_widget.directory_rename_requested.connect(self.rename_directory_in_project)

        # CodeEditorTabWidget signals
        self.code_editor_widget.current_editor_changed.connect(self.on_current_editor_changed)
        self.code_editor_widget.editor_text_changed.connect(self.on_editor_text_changed)
        self.code_editor_widget.editor_file_saved.connect(self.on_editor_file_saved)
        self.code_editor_widget.editor_file_closed.connect(self.on_editor_file_closed)

        # PreviewEngine signals
        self.preview_engine_widget.title_changed.connect(self.on_preview_title_changed)
        self.preview_engine_widget.url_changed.connect(self.on_preview_url_changed)

        # PromptsPanel signals
        self.prompts_panel_widget.prompt_to_apply.connect(self.on_prompt_to_apply)
        self.prompts_panel_widget.prompt_associated.connect(self.on_prompt_associated)

        # View Actions
        self.toggle_line_numbers_action.triggered.connect(self.code_editor_widget.set_line_numbers_visible)

    def setup_ui(self):
        """
        Set up the main window's UI layout.
        """
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # Create the main horizontal splitter for left, center, and right panels
        self.main_horizontal_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_layout.addWidget(self.main_horizontal_splitter)

        # --- Left Panel: Project Explorer ---
        self.file_tree_widget = FileTreeWidget(self.file_manager, self)
        self.file_tree_widget.setMinimumWidth(250)
        self.file_tree_widget.setMaximumWidth(500)
        self.main_horizontal_splitter.addWidget(self.file_tree_widget)

        # --- Center Panel: Tabbed Code Editor ---
        self.code_editor_widget = CodeEditorTabWidget(self.file_manager, self)
        self.main_horizontal_splitter.addWidget(self.code_editor_widget)

        # --- Right Panel: Live HTML Preview ---
        self.preview_engine_widget = PreviewEngine(self.file_manager, self)
        self.preview_engine_widget.setMinimumWidth(300)
        self.main_horizontal_splitter.addWidget(self.preview_engine_widget)

        # Set initial sizes for the horizontal splitter (Left:Center:Right)
        # 300px for left, 600px for center, 300px for right (out of 1200px total)
        self.main_horizontal_splitter.setSizes([300, 600, 300])

        # --- Bottom Panel: AI Prompts (Collapsible) ---
        # This will be a vertical splitter with the main_horizontal_splitter and the prompts panel
        self.main_vertical_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Add the existing horizontal splitter to the vertical one
        self.main_vertical_splitter.addWidget(self.main_horizontal_splitter)
        
        self.prompts_panel_widget = PromptsPanel(self.file_manager, self)
        self.prompts_panel_widget.setMinimumHeight(150)
        self.prompts_panel_widget.setMaximumHeight(400)
        self.main_vertical_splitter.addWidget(self.prompts_panel_widget)
        
        # Replace the central widget layout with the vertical splitter
        self.central_widget.layout().addWidget(self.main_vertical_splitter)
        
        # Set initial sizes for the vertical splitter (Main:Bottom)
        # 650px for main area, 150px for prompts panel (out of 800px total height)
        self.main_vertical_splitter.setSizes([650, 150])


    def create_actions(self):
        """
        Create application actions for menus and toolbars.
        """
        # File Actions
        self.new_project_action = QAction(QIcon.fromTheme("document-new", QIcon()), "New Project...", self)
        self.new_project_action.setShortcut("Ctrl+Shift+N")
        self.new_project_action.setStatusTip("Create a new project")
        self.new_project_action.triggered.connect(self.new_project)

        self.open_project_action = QAction(QIcon.fromTheme("document-open", QIcon()), "Open Project...", self)
        self.open_project_action.setShortcut("Ctrl+O")
        self.open_project_action.setStatusTip("Open an existing project")
        self.open_project_action.triggered.connect(self.open_project)

        self.close_project_action = QAction("Close Project", self)
        self.close_project_action.setStatusTip("Close the current project")
        self.close_project_action.triggered.connect(self.close_project)
        self.close_project_action.setEnabled(False)

        self.save_file_action = QAction(QIcon.fromTheme("document-save", QIcon()), "Save", self)
        self.save_file_action.setShortcut("Ctrl+S")
        self.save_file_action.setStatusTip("Save the current file")
        self.save_file_action.triggered.connect(self.save_file)
        self.save_file_action.setEnabled(False)

        self.save_all_action = QAction(QIcon.fromTheme("document-save-as", QIcon()), "Save All", self)
        self.save_all_action.setShortcut("Ctrl+Shift+S")
        self.save_all_action.setStatusTip("Save all open files")
        self.save_all_action.triggered.connect(self.save_all)
        self.save_all_action.setEnabled(False)

        self.exit_action = QAction(QIcon.fromTheme("application-exit", QIcon()), "Exit", self)
        self.exit_action.setShortcut("Alt+F4")
        self.exit_action.setStatusTip("Exit the application")
        self.exit_action.triggered.connect(self.close)

        # Edit Actions (Placeholders)
        self.undo_action = QAction(QIcon.fromTheme("edit-undo", QIcon()), "Undo", self)
        self.undo_action.setShortcut("Ctrl+Z")
        
        self.redo_action = QAction(QIcon.fromTheme("edit-redo", QIcon()), "Redo", self)
        self.redo_action.setShortcut("Ctrl+Y")

        self.cut_action = QAction(QIcon.fromTheme("edit-cut", QIcon()), "Cut", self)
        self.cut_action.setShortcut("Ctrl+X")

        self.copy_action = QAction(QIcon.fromTheme("edit-copy", QIcon()), "Copy", self)
        self.copy_action.setShortcut("Ctrl+C")

        self.paste_action = QAction(QIcon.fromTheme("edit-paste", QIcon()), "Paste", self)
        self.paste_action.setShortcut("Ctrl+V")

        # View Actions (Placeholders)
        self.toggle_line_numbers_action = QAction("Toggle Line Numbers", self)
        self.toggle_line_numbers_action.setCheckable(True)
        self.toggle_line_numbers_action.setChecked(True) # Assuming on by default

        self.toggle_prompts_panel_action = QAction("Toggle Prompts Panel", self)
        self.toggle_prompts_panel_action.setCheckable(True)
        self.toggle_prompts_panel_action.setChecked(True)
        self.toggle_prompts_panel_action.triggered.connect(self.toggle_prompts_panel)

        # Help Actions (Placeholders)
        self.about_action = QAction("About HTML Dev Studio", self)
        self.about_action.triggered.connect(self.about)

    def create_menus(self):
        """
        Create the application's menu bar.
        """
        menubar = self.menuBar()

        # File Menu
        file_menu = menubar.addMenu("&File")
        file_menu.addAction(self.new_project_action)
        file_menu.addAction(self.open_project_action)
        file_menu.addAction(self.close_project_action)
        file_menu.addSeparator()
        file_menu.addAction(self.save_file_action)
        file_menu.addAction(self.save_all_action)
        file_menu.addSeparator()
        file_menu.addAction(self.exit_action)

        # Edit Menu
        edit_menu = menubar.addMenu("&Edit")
        edit_menu.addAction(self.undo_action)
        edit_menu.addAction(self.redo_action)
        edit_menu.addSeparator()
        edit_menu.addAction(self.cut_action)
        edit_menu.addAction(self.copy_action)
        edit_menu.addAction(self.paste_action)

        # View Menu
        view_menu = menubar.addMenu("&View")
        view_menu.addAction(self.toggle_line_numbers_action)
        view_menu.addAction(self.toggle_prompts_panel_action)

        # Tools Menu (Placeholder)
        tools_menu = menubar.addMenu("&Tools")
        # Add tool-specific actions here

        # Help Menu
        help_menu = menubar.addMenu("&Help")
        help_menu.addAction(self.about_action)

    def create_toolbars(self):
        """
        Create the application's toolbars.
        """
        self.main_toolbar = QToolBar("Main Toolbar")
        self.main_toolbar.setIconSize(QSize(16, 16))
        self.addToolBar(self.main_toolbar)
        
        self.main_toolbar.addAction(self.new_project_action)
        self.main_toolbar.addAction(self.open_project_action)
        self.main_toolbar.addSeparator()
        self.main_toolbar.addAction(self.save_file_action)
        self.main_toolbar.addAction(self.save_all_action)

    def create_status_bar(self):
        """
        Create the application's status bar.
        """
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def load_settings(self):
        """
        Load application settings (e.g., window geometry, recent projects).
        """
        # Placeholder for loading settings using QSettings
        pass

    def save_settings(self):
        """
        Save application settings.
        """
        # Placeholder for saving settings using QSettings
        pass

    # --- Action Slots ---
    def new_project(self):
        """
        Handle the 'New Project' action.
        """
        QMessageBox.information(self, "New Project", "New Project functionality to be implemented.")

    def open_project(self):
        """
        Handle the 'Open Project' action.
        """
        folder_path = QFileDialog.getExistingDirectory(self, "Open Project Directory", "", 
                                                      QFileDialog.Option.ShowDirsOnly)
        if folder_path:
            self.current_project_path = folder_path
            self.setWindowTitle(f"HTML Dev Studio - {os.path.basename(folder_path)}")
            self.status_bar.showMessage(f"Project opened: {folder_path}")
            self.close_project_action.setEnabled(True)
            # TODO: Signal file_tree_widget to load the project
            # TODO: Enable other project-dependent actions

    def close_project(self):
        """
        Handle the 'Close Project' action.
        """
        if self.current_project_path:
            # TODO: Prompt to save unsaved files
            self.current_project_path = None
            self.setWindowTitle("HTML Dev Studio")
            self.status_bar.showMessage("Project closed.")
            self.close_project_action.setEnabled(False)
            # TODO: Signal file_tree_widget to clear
            # TODO: Disable project-dependent actions

    def save_file(self):
        """
        Handle the 'Save File' action.
        """
        QMessageBox.information(self, "Save File", "Save File functionality to be implemented.")

    def save_all(self):
        """
        Handle the 'Save All' action.
        """
        QMessageBox.information(self, "Save All", "Save All functionality to be implemented.")

    def toggle_prompts_panel(self, checked):
        """
        Handle the 'Toggle Prompts Panel' action.
        """
        self.prompts_panel_widget.setVisible(checked)
        # Adjust splitter sizes if needed, or let it handle visibility

    def about(self):
        """
        Handle the 'About' action.
        """
        QMessageBox.about(self, "About HTML Dev Studio",
                          "<b>HTML Dev Studio</b><br>"
                          "Version 1.0.0<br><br>"
                          "A comprehensive HTML/CSS/JavaScript development environment.<br><br>"
                          "Created with PyQt6.")

    # --- Signal Handling Slots ---

    def on_file_manager_error(self, message):
        """Displays an error message from the FileManager."""
        QMessageBox.critical(self, "File System Error", message)

    def on_file_opened_by_manager(self, file_path, content):
        """Called when FileManager successfully opens a file."""
        # This is usually handled by open_file_in_editor which calls file_manager.open_file
        # This slot can be used for additional UI updates if needed.
        self.status_bar.showMessage(f"Opened: {self.file_manager.get_relative_path(file_path)}", 3000)

    def on_file_saved_by_manager(self, file_path):
        """Called when FileManager successfully saves a file."""
        self.status_bar.showMessage(f"Saved: {self.file_manager.get_relative_path(file_path)}", 3000)
        # If the saved file is the one being previewed, refresh preview
        if file_path == self.preview_engine_widget.current_html_path:
             self.preview_engine_widget.refresh_preview()


    def on_file_created_by_manager(self, file_path):
        """Called when FileManager creates a new file."""
        self.status_bar.showMessage(f"Created: {self.file_manager.get_relative_path(file_path)}", 3000)
        # Optionally open the new file
        # self.open_file_in_editor(file_path)

    def on_file_deleted_by_manager(self, file_path):
        """Called when FileManager deletes a file."""
        self.status_bar.showMessage(f"Deleted: {self.file_manager.get_relative_path(file_path)}", 3000)
        # If the deleted file was open in an editor, close that tab
        if self.code_editor_widget.is_file_open(file_path):
            self.code_editor_widget.close_file(file_path)
        # If the deleted file was being previewed, clear preview
        if file_path == self.preview_engine_widget.current_html_path:
            self.preview_engine_widget.clear_preview()


    def on_file_renamed_by_manager(self, old_path, new_path):
        """Called when FileManager renames a file."""
        self.status_bar.showMessage(f"Renamed '{os.path.basename(old_path)}' to '{os.path.basename(new_path)}'", 3000)
        # If the renamed file was open, update the editor tab
        if self.code_editor_widget.is_file_open(old_path):
            self.code_editor_widget.update_file_path_in_tab(old_path, new_path) # Assuming this method exists
        # If the renamed file was being previewed, update preview
        if old_path == self.preview_engine_widget.current_html_path:
            self.preview_engine_widget.load_html_file(new_path)


    def on_directory_created_by_manager(self, dir_path):
        """Called when FileManager creates a new directory."""
        self.status_bar.showMessage(f"Created directory: {self.file_manager.get_relative_path(dir_path)}", 3000)

    def on_directory_deleted_by_manager(self, dir_path):
        """Called when FileManager deletes a directory."""
        self.status_bar.showMessage(f"Deleted directory: {self.file_manager.get_relative_path(dir_path)}", 3000)
        # Close any open files that were in this directory
        self.code_editor_widget.close_files_in_directory(dir_path) # Assuming this method exists
        # Clear preview if it was a file from this directory
        if self.preview_engine_widget.current_html_path and self.preview_engine_widget.current_html_path.startswith(dir_path):
            self.preview_engine_widget.clear_preview()


    # --- FileTreeWidget Interaction Slots ---
    def open_file_in_editor(self, file_path):
        """Opens a file from the file tree into the code editor."""
        if not self.code_editor_widget.is_file_open(file_path):
            if self.file_manager.open_file(file_path): # FileManager reads and caches
                content = self.file_manager.get_open_file_content(file_path)
                if content is not None:
                    self.code_editor_widget.open_file(file_path, content)
                    # If it's an HTML file, update preview
                    if file_path.lower().endswith(('.html', '.htm')):
                        self.preview_engine_widget.load_html_file(file_path)
        else:
            # File is already open, just switch to its tab
            self.code_editor_widget.switch_to_file(file_path)


    def create_new_file_in_project(self, dir_path, file_name):
        """Creates a new file in the specified directory."""
        full_path = os.path.join(dir_path, file_name)
        self.file_manager.create_new_file(full_path) # FileManager handles creation and signaling

    def delete_file_in_project(self, file_path):
        """Deletes a file."""
        self.file_manager.delete_file(file_path) # FileManager handles deletion and signaling

    def rename_file_in_project(self, old_path, new_name):
        """Renames a file."""
        new_path = os.path.join(os.path.dirname(old_path), new_name)
        self.file_manager.rename_file(old_path, new_path) # FileManager handles renaming and signaling

    def create_new_directory_in_project(self, parent_dir, new_dir_name):
        """Creates a new directory."""
        new_dir_path = os.path.join(parent_dir, new_dir_name)
        self.file_manager.create_directory(new_dir_path)

    def delete_directory_in_project(self, dir_path):
        """Deletes a directory."""
        self.file_manager.delete_directory(dir_path)

    def rename_directory_in_project(self, old_path, new_name):
        """Renames a directory."""
        new_path = os.path.join(os.path.dirname(old_path), new_name)
        self.file_manager.rename_file(old_path, new_path) # rename_file works for dirs too


    # --- CodeEditorTabWidget Interaction Slots ---
    def on_current_editor_changed(self, file_path):
        """Called when the active editor tab changes."""
        if file_path:
            self.status_bar.showMessage(f"Current file: {self.file_manager.get_relative_path(file_path)}")
            self.save_file_action.setEnabled(True)
            # If it's an HTML file, ensure it's in preview
            if file_path.lower().endswith(('.html', '.htm')):
                if self.preview_engine_widget.current_html_path != file_path:
                    self.preview_engine_widget.load_html_file(file_path)
            # Update prompts panel association
            self.prompts_panel_widget.set_associated_file_for_current_prompt(file_path)
        else:
            self.status_bar.showMessage("No active file.")
            self.save_file_action.setEnabled(False)
            # self.preview_engine_widget.clear_preview() # Optional: clear preview if no file

    def on_editor_text_changed(self, file_path):
        """Called when text in an editor changes."""
        self.save_file_action.setEnabled(True)
        self.save_all_action.setEnabled(True)
        # Schedule a preview refresh if it's an HTML file
        if file_path and file_path.lower().endswith(('.html', '.htm')):
            self.preview_engine_widget.schedule_refresh()

    def on_editor_file_saved(self, file_path):
        """Called when an editor saves its file."""
        # Actual saving is done by CodeEditor calling file_manager.save_file
        # This slot is for UI updates after the fact.
        self.status_bar.showMessage(f"Saved: {self.file_manager.get_relative_path(file_path)}", 3000)
        # Refresh preview if it's the active HTML file
        if file_path == self.preview_engine_widget.current_html_path:
            self.preview_engine_widget.refresh_preview()
        
        # Check if all files are saved to disable save_all if appropriate
        if not self.code_editor_widget.has_dirty_files():
            self.save_all_action.setEnabled(False)
        if not self.code_editor_widget.is_current_editor_dirty(): # Check current specific editor
             self.save_file_action.setEnabled(False)


    def on_editor_file_closed(self, file_path):
        """Called when an editor tab is closed."""
        self.status_bar.showMessage(f"Closed: {self.file_manager.get_relative_path(file_path)}", 3000)
        # If the closed file was being previewed, clear or update preview
        if file_path == self.preview_engine_widget.current_html_path:
            # Check if another HTML file is open to preview, else clear
            current_open_html_file = self.code_editor_widget.get_current_open_html_file() # Assuming this method
            if current_open_html_file:
                self.preview_engine_widget.load_html_file(current_open_html_file)
            else:
                self.preview_engine_widget.clear_preview()
        
        # Update save actions
        if not self.code_editor_widget.has_dirty_files():
            self.save_all_action.setEnabled(False)
        if not self.code_editor_widget.is_current_editor_dirty():
             self.save_file_action.setEnabled(False)


    # --- PreviewEngine Interaction Slots ---
    def on_preview_title_changed(self, title):
        """Called when the preview page title changes."""
        # Could update a status bar label or window title supplement
        pass

    def on_preview_url_changed(self, url):
        """Called when the preview page URL changes."""
        # Could update a status bar label
        pass


    # --- PromptsPanel Interaction Slots ---
    def on_prompt_to_apply(self, prompt_text, associated_file_path):
        """
        Called when a prompt is to be applied.
        For now, this will just show a message. In a real app, this would
        interact with an AI service.
        """
        target_file_msg = f" (for {os.path.basename(associated_file_path)})" if associated_file_path and associated_file_path != "None" else ""
        QMessageBox.information(self, "Apply Prompt", 
                                f"Prompt to apply:\n\n\"{prompt_text[:100]}...\"\n\n{target_file_msg}\n\n(AI integration not yet implemented)")
        # If associated_file_path is provided and valid, ensure it's open in editor
        if associated_file_path and associated_file_path != "None" and os.path.exists(associated_file_path):
            if not self.code_editor_widget.is_file_open(associated_file_path):
                self.open_file_in_editor(associated_file_path)
            self.code_editor_widget.switch_to_file(associated_file_path)


    def on_prompt_associated(self, prompt_id, file_path):
        """Called when a prompt is associated with a file."""
        self.status_bar.showMessage(f"Prompt associated with: {os.path.basename(file_path)}", 3000)


    # --- Action Slots (Updated Implementations) ---
    def new_project(self):
        """
        Handle the 'New Project' action.
        For now, this is similar to opening a project but could involve creating a dir.
        """
        folder_path = QFileDialog.getExistingDirectory(self, "Select New Project Directory (or create one)", "", 
                                                      QFileDialog.Option.ShowDirsOnly)
        if folder_path:
            # Optionally, create a default project structure here
            self.current_project_path = folder_path
            self.file_manager.set_project_path(self.current_project_path)
            self.file_tree_widget.set_project_path(self.current_project_path)
            self.preview_engine_widget.set_project_path(self.current_project_path)
            self.code_editor_widget.close_all_tabs() # Clear any open files from previous project
            self.preview_engine_widget.clear_preview()
            self.setWindowTitle(f"HTML Dev Studio - {os.path.basename(folder_path)} (New Project)")
            self.status_bar.showMessage(f"New project location: {folder_path}")
            self.close_project_action.setEnabled(True)
            self.enable_project_dependent_actions(True)

    def open_project(self):
        """
        Handle the 'Open Project' action.
        """
        folder_path = QFileDialog.getExistingDirectory(self, "Open Project Directory", "", 
                                                      QFileDialog.Option.ShowDirsOnly)
        if folder_path:
            self.current_project_path = folder_path
            self.file_manager.set_project_path(self.current_project_path)
            self.file_tree_widget.set_project_path(self.current_project_path)
            self.preview_engine_widget.set_project_path(self.current_project_path)
            self.code_editor_widget.close_all_tabs() # Clear any open files from previous project
            self.preview_engine_widget.clear_preview()
            self.setWindowTitle(f"HTML Dev Studio - {os.path.basename(folder_path)}")
            self.status_bar.showMessage(f"Project opened: {folder_path}")
            self.close_project_action.setEnabled(True)
            self.enable_project_dependent_actions(True)

    def close_project(self):
        """
        Handle the 'Close Project' action.
        """
        if self.current_project_path:
            # TODO: Prompt to save unsaved files using code_editor_widget.has_dirty_files()
            if self.code_editor_widget.has_dirty_files():
                reply = QMessageBox.question(self, "Unsaved Changes",
                                             "The project has unsaved changes. Are you sure you want to close it?",
                                             QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                             QMessageBox.StandardButton.No)
                if reply == QMessageBox.StandardButton.No:
                    return

            self.current_project_path = None
            self.file_manager.set_project_path(None)
            self.file_tree_widget.set_project_path(None)
            self.preview_engine_widget.set_project_path(None)
            self.code_editor_widget.close_all_tabs()
            self.preview_engine_widget.clear_preview()
            self.setWindowTitle("HTML Dev Studio")
            self.status_bar.showMessage("Project closed.")
            self.close_project_action.setEnabled(False)
            self.enable_project_dependent_actions(False)

    def save_file(self):
        """
        Handle the 'Save File' action.
        """
        self.code_editor_widget.save_current_file()

    def save_all(self):
        """
        Handle the 'Save All' action.
        """
        self.code_editor_widget.save_all_files()

    def enable_project_dependent_actions(self, enable):
        """Enables or disables actions that require an open project."""
        # self.save_file_action.setEnabled(enable) # This is handled by editor state
        # self.save_all_action.setEnabled(enable) # This is handled by editor state
        # Add other project-dependent actions here if any
        pass

    def closeEvent(self, event):
        """
        Handle the application close event.
        """
        if self.code_editor_widget.has_dirty_files():
            reply = QMessageBox.question(self, "Unsaved Changes",
                                         "You have unsaved changes. Are you sure you want to exit?",
                                         QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                         QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
        
        self.save_settings()
        # Clean up temp files from preview
        self.preview_engine_widget.cleanup_temp_file()
        event.accept()
