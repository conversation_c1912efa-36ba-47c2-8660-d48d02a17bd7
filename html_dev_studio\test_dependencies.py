#!/usr/bin/env python3
"""
Test script to verify all dependencies are installed correctly.
"""

def test_imports():
    """Test all required imports."""
    try:
        print("Testing PyQt6 imports...")
        
        # Core PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QUrl, QTimer, pyqtSignal, QSize
        from PyQt6.QtGui import QIcon, QAction
        print("✓ PyQt6 core modules imported successfully")
        
        # WebEngine
        from PyQt6.QtWebEngineWidgets import QWebEngineView
        from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile
        print("✓ PyQt6 WebEngine modules imported successfully")
        
        # Other dependencies
        import chardet
        print("✓ chardet imported successfully")
        
        import watchdog
        print("✓ watchdog imported successfully")
        
        # Pygments for syntax highlighting
        import pygments
        from pygments.lexers import get_lexer_by_name
        from pygments.formatters import get_formatter_by_name
        print("✓ Pygments imported successfully")
        
        print("\n🎉 All dependencies are installed correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic PyQt6 functionality."""
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # Create a minimal application to test Qt functionality
        app = QApplication(sys.argv)
        print("✓ QApplication created successfully")
        
        # Test that we can create basic widgets
        from PyQt6.QtWidgets import QWidget, QLabel
        widget = QWidget()
        label = QLabel("Test", widget)
        print("✓ Basic widgets created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("HTML Dev Studio - Dependency Test")
    print("=" * 40)
    
    imports_ok = test_imports()
    print()
    
    if imports_ok:
        functionality_ok = test_basic_functionality()
        
        if functionality_ok:
            print("\n✅ All tests passed! The application should work correctly.")
        else:
            print("\n⚠️  Imports work but basic functionality failed.")
    else:
        print("\n❌ Some dependencies are missing. Please install them using:")
        print("pip install -r requirements.txt")
