import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem, 
                             QMenu, QMessageBox, QInputDialog, QFileDialog)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QIcon, QAction

# Placeholder for FileManager, will be properly connected later
# from core.file_manager import FileManager 

class FileTreeWidget(QWidget):
    """
    A widget that displays a project's file structure in a tree view.
    """
    # Signals
    file_open_requested = pyqtSignal(str)  # file_path
    file_rename_requested = pyqtSignal(str, str) # old_path, new_name
    file_delete_requested = pyqtSignal(str) # file_path
    file_create_requested = pyqtSignal(str, str) # dir_path, file_name
    directory_create_requested = pyqtSignal(str, str) # parent_dir, new_dir_name
    directory_delete_requested = pyqtSignal(str) # dir_path
    directory_rename_requested = pyqtSignal(str, str) # old_path, new_name

    def __init__(self, file_manager, parent=None):
        super().__init__(parent)
        self.file_manager = file_manager # Instance of FileManager
        self.current_project_path = None
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """
        Set up the file tree UI.
        """
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)

        self.tree_widget = QTreeWidget(self)
        self.tree_widget.setHeaderHidden(True)
        self.tree_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.show_context_menu)
        self.tree_widget.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # Placeholder for icons - replace with actual icons later
        self.folder_icon = QIcon.fromTheme("folder", QIcon())
        self.html_icon = QIcon.fromTheme("text-html", QIcon())
        self.css_icon = QIcon.fromTheme("text-css", QIcon()) # May not exist, fallback
        self.js_icon = QIcon.fromTheme("text-x-script", QIcon()) # May not exist, fallback
        self.generic_file_icon = QIcon.fromTheme("text-x-generic", QIcon())

        self.layout.addWidget(self.tree_widget)

    def connect_signals(self):
        """
        Connect signals from FileManager to update the tree.
        """
        if self.file_manager:
            self.file_manager.file_created.connect(self.refresh_tree)
            self.file_manager.file_deleted.connect(self.refresh_tree)
            self.file_manager.file_renamed.connect(self.refresh_tree)
            self.file_manager.directory_created.connect(self.refresh_tree)
            self.file_manager.directory_deleted.connect(self.refresh_tree)
            self.file_manager.error_occurred.connect(self.on_file_manager_error)

    def set_project_path(self, path):
        """
        Sets the project path and populates the tree.
        """
        if path and os.path.isdir(path):
            self.current_project_path = path
            self.populate_tree(path)
        else:
            self.current_project_path = None
            self.tree_widget.clear()

    def populate_tree(self, root_path):
        """
        Populates the tree widget with files and directories from the root_path.
        """
        self.tree_widget.clear()
        if not root_path or not os.path.isdir(root_path):
            return

        root_item = self._add_item_to_tree(None, root_path, is_root=True)
        self._populate_directory(root_item, root_path)
        root_item.setExpanded(True)

    def _populate_directory(self, parent_item, dir_path):
        """
        Recursively populates a directory's contents into the tree.
        """
        items = self.file_manager.list_directory(dir_path)
        for item_info in items:
            if item_info['is_dir']:
                dir_item = self._add_item_to_tree(parent_item, item_info['path'])
                self._populate_directory(dir_item, item_info['path']) # Recurse
            elif self.file_manager.is_valid_project_file(item_info['path']):
                self._add_item_to_tree(parent_item, item_info['path'])

    def _add_item_to_tree(self, parent_item, path, is_root=False):
        """
        Adds a single item (file or directory) to the tree.
        """
        name = os.path.basename(path) if not is_root else os.path.basename(os.path.dirname(path)) # Project name for root
        is_dir = os.path.isdir(path)

        item = QTreeWidgetItem(parent_item if parent_item else self.tree_widget)
        item.setText(0, name)
        item.setData(0, Qt.ItemDataRole.UserRole, path) # Store full path
        item.setToolTip(0, path)

        if is_root:
            item.setIcon(0, self.folder_icon)
        elif is_dir:
            item.setIcon(0, self.folder_icon)
        else:
            ext = os.path.splitext(path)[1].lower()
            if ext in {'.html', '.htm'}:
                item.setIcon(0, self.html_icon)
            elif ext == '.css':
                item.setIcon(0, self.css_icon)
            elif ext == '.js':
                item.setIcon(0, self.js_icon)
            else:
                item.setIcon(0, self.generic_file_icon)
        return item

    def refresh_tree(self, changed_path=None):
        """
        Refreshes the file tree. If changed_path is given, it tries to refresh
        only that part of the tree, otherwise, it refreshes the whole tree.
        """
        if self.current_project_path:
            # For simplicity, full refresh for now.
            # Can be optimized to refresh only the changed directory.
            self.populate_tree(self.current_project_path)

    def on_item_double_clicked(self, item, column):
        """
        Handles double-click on a tree item.
        """
        path = item.data(0, Qt.ItemDataRole.UserRole)
        if path and os.path.isfile(path):
            self.file_open_requested.emit(path)

    def show_context_menu(self, position):
        """
        Shows a context menu for the item at the given position.
        """
        item = self.tree_widget.itemAt(position)
        if not item:
            # Context menu for empty space (create new file/folder at root)
            menu = QMenu(self)
            new_file_action = QAction("New File...", self)
            new_folder_action = QAction("New Folder...", self)
            
            new_file_action.triggered.connect(lambda: self.create_new_item(self.current_project_path, is_file=True))
            new_folder_action.triggered.connect(lambda: self.create_new_item(self.current_project_path, is_file=False))
            
            menu.addAction(new_file_action)
            menu.addAction(new_folder_action)
            menu.exec(self.tree_widget.viewport().mapToGlobal(position))
            return

        path = item.data(0, Qt.ItemDataRole.UserRole)
        is_dir = os.path.isdir(path)

        menu = QMenu(self)

        # Common actions
        open_action = QAction("Open", self)
        rename_action = QAction("Rename", self)
        delete_action = QAction("Delete", self)
        
        open_action.triggered.connect(lambda: self.file_open_requested.emit(path))
        rename_action.triggered.connect(lambda: self.rename_item(path))
        delete_action.triggered.connect(lambda: self.delete_item(path))

        if is_dir:
            menu.addAction(open_action) # Open means expand/collapse or open in explorer?
            menu.addAction(new_file_action := QAction("New File...", self))
            menu.addAction(new_folder_action := QAction("New Folder...", self))
            menu.addSeparator()
            menu.addAction(rename_action)
            menu.addAction(delete_action)

            new_file_action.triggered.connect(lambda: self.create_new_item(path, is_file=True))
            new_folder_action.triggered.connect(lambda: self.create_new_item(path, is_file=False))
        else:
            menu.addAction(open_action)
            menu.addSeparator()
            menu.addAction(rename_action)
            menu.addAction(delete_action)
        
        menu.exec(self.tree_widget.viewport().mapToGlobal(position))

    def create_new_item(self, parent_path, is_file):
        """
        Prompts user for a name and creates a new file or directory.
        """
        if not parent_path or not os.path.isdir(parent_path):
            QMessageBox.warning(self, "Error", "Invalid parent directory.")
            return

        item_type = "file" if is_file else "folder"
        name, ok = QInputDialog.getText(self, f"New {item_type.capitalize()}", 
                                        f"Enter {item_type} name:")
        if ok and name:
            new_path = os.path.join(parent_path, name)
            if is_file:
                # Ensure it has a valid extension if not provided
                if not os.path.splitext(name)[1] and self.file_manager.is_valid_project_file(name + ".html"):
                    new_path += ".html" # Default to .html
                self.file_create_requested.emit(parent_path, os.path.basename(new_path))
            else:
                self.directory_create_requested.emit(parent_path, name)

    def rename_item(self, old_path):
        """
        Prompts user for a new name and renames the item.
        """
        if not old_path or not os.path.exists(old_path):
            QMessageBox.warning(self, "Error", "Invalid item to rename.")
            return

        current_name = os.path.basename(old_path)
        new_name, ok = QInputDialog.getText(self, "Rename Item", 
                                            f"Rename '{current_name}' to:", 
                                            text=current_name)
        if ok and new_name and new_name != current_name:
            new_path = os.path.join(os.path.dirname(old_path), new_name)
            if os.path.isdir(old_path):
                self.directory_rename_requested.emit(old_path, new_name)
            else:
                self.file_rename_requested.emit(old_path, new_name)

    def delete_item(self, path):
        """
        Prompts user for confirmation and deletes the item.
        """
        if not path or not os.path.exists(path):
            QMessageBox.warning(self, "Error", "Invalid item to delete.")
            return
            
        item_name = os.path.basename(path)
        is_dir = os.path.isdir(path)
        item_type = "folder and all its contents" if is_dir else "file"
        
        reply = QMessageBox.question(self, "Confirm Delete", 
                                     f"Are you sure you want to delete '{item_name}' ({item_type})?",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                     QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            if is_dir:
                self.directory_delete_requested.emit(path)
            else:
                self.file_delete_requested.emit(path)

    def on_file_manager_error(self, message):
        """
        Displays an error message from the FileManager.
        """
        QMessageBox.critical(self, "File Operation Error", message)

    def get_selected_item_path(self):
        """
        Returns the path of the currently selected item, or None.
        """
        selected_items = self.tree_widget.selectedItems()
        if selected_items:
            return selected_items[0].data(0, Qt.ItemDataRole.UserRole)
        return None
